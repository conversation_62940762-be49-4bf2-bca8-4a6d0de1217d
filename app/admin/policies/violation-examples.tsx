import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function ViolationExamples() {
  const exampleCategories = [
    {
      name: 'Clear Violations',
      description: 'Obvious policy breaches with no ambiguity',
      color: '#e74c3c',
      examples: 12
    },
    {
      name: 'Borderline Cases',
      description: 'Difficult moderation decisions requiring judgment',
      color: '#f39c12',
      examples: 8
    },
    {
      name: 'Context-Dependent',
      description: 'Violations that depend on situational context',
      color: '#3498db',
      examples: 15
    },
    {
      name: 'False Positives',
      description: 'Content incorrectly flagged by automated systems',
      color: '#27ae60',
      examples: 6
    }
  ];

  const recentCaseStudies = [
    {
      id: 'cs001',
      title: 'Hate Speech vs. Political Satire',
      category: 'Borderline Cases',
      date: '2024-01-20',
      decision: 'Not Violation',
      moderator: 'Admin Team'
    },
    {
      id: 'cs002',
      title: 'Spam in Private Groups',
      category: 'Clear Violations',
      date: '2024-01-19',
      decision: 'Violation',
      moderator: 'Auto-Detection'
    },
    {
      id: 'cs003',
      title: 'Cultural Context in Stories',
      category: 'Context-Dependent',
      date: '2024-01-18',
      decision: 'Not Violation',
      moderator: 'Senior Moderator'
    }
  ];

  const violationTypes = [
    { type: 'Harassment', count: 45, trend: 'up' },
    { type: 'Hate Speech', count: 23, trend: 'down' },
    { type: 'Spam', count: 67, trend: 'up' },
    { type: 'Violence', count: 12, trend: 'stable' },
    { type: 'Sexual Content', count: 34, trend: 'down' },
    { type: 'Terrorism', count: 3, trend: 'stable' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Violation Examples</Text>
        <Text style={styles.subtitle}>Moderation Guidelines and Case Studies</Text>
        <Link href="/admin/policies/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Policies</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a comprehensive library of violation examples, moderation
          guidelines, and case studies to ensure consistent decision-making across all
          content moderation activities. It includes training materials and precedent cases.
        </Text>

        <Text style={styles.sectionTitle}>Example Categories:</Text>
        <View style={styles.categoriesContainer}>
          {exampleCategories.map((category, index) => (
            <TouchableOpacity key={index} style={[styles.categoryCard, { borderLeftColor: category.color }]}>
              <View style={styles.categoryHeader}>
                <Text style={styles.categoryName}>{category.name}</Text>
                <View style={[styles.exampleCount, { backgroundColor: category.color }]}>
                  <Text style={styles.countText}>{category.examples}</Text>
                </View>
              </View>
              <Text style={styles.categoryDescription}>{category.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Recent Case Studies:</Text>
        <View style={styles.caseStudiesContainer}>
          {recentCaseStudies.map((study, index) => (
            <View key={index} style={styles.caseStudyItem}>
              <View style={styles.caseStudyHeader}>
                <Text style={styles.caseStudyTitle}>{study.title}</Text>
                <View style={[styles.decisionBadge, { 
                  backgroundColor: study.decision === 'Violation' ? '#e74c3c' : '#27ae60' 
                }]}>
                  <Text style={styles.decisionText}>{study.decision}</Text>
                </View>
              </View>
              <Text style={styles.caseStudyCategory}>{study.category}</Text>
              <View style={styles.caseStudyMeta}>
                <Text style={styles.metaText}>Date: {study.date}</Text>
                <Text style={styles.metaText}>Moderator: {study.moderator}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Violation Statistics (Last 30 Days):</Text>
        <View style={styles.statisticsContainer}>
          {violationTypes.map((violation, index) => (
            <View key={index} style={styles.statisticItem}>
              <View style={styles.statisticInfo}>
                <Text style={styles.violationType}>{violation.type}</Text>
                <Text style={styles.violationCount}>{violation.count} cases</Text>
              </View>
              <View style={styles.trendIndicator}>
                <Text style={[styles.trendText, { 
                  color: violation.trend === 'up' ? '#e74c3c' : 
                        violation.trend === 'down' ? '#27ae60' : '#95a5a6' 
                }]}>
                  {violation.trend === 'up' ? '↗' : violation.trend === 'down' ? '↘' : '→'}
                </Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Moderation Tools:</Text>
        <View style={styles.toolsContainer}>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>📚</Text>
            <Text style={styles.toolTitle}>Training Library</Text>
            <Text style={styles.toolDescription}>Comprehensive moderation training materials</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>🔍</Text>
            <Text style={styles.toolTitle}>Case Search</Text>
            <Text style={styles.toolDescription}>Search historical moderation decisions</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>📊</Text>
            <Text style={styles.toolTitle}>Decision Analytics</Text>
            <Text style={styles.toolDescription}>Analyze moderation patterns and trends</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
            <Text style={styles.actionButtonText}>📝 Add New Example</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionButtonText}>📋 Export Guidelines</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#f39c12' }]}>
            <Text style={styles.actionButtonText}>🎓 Training Mode</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Related Navigation:</Text>
        <View style={styles.navigationSection}>
          <Link href="/admin/policies/community-rules" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>📜 Community Rules</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/content/moderation-dashboard" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>🛡️ Moderation Dashboard</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/policies/implementation" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>⚙️ Policy Implementation</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Features Available:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Violation example library with multimedia content</Text>
          <Text style={styles.feature}>• Moderation decision precedents and reasoning</Text>
          <Text style={styles.feature}>• Interactive training materials for new moderators</Text>
          <Text style={styles.feature}>• Case study database with search functionality</Text>
          <Text style={styles.feature}>• Decision consistency tracking and analytics</Text>
          <Text style={styles.feature}>• Multi-language support for global moderation</Text>
          <Text style={styles.feature}>• Integration with content moderation workflows</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Category Selection → Example Library → Detailed Case Studies
          {'\n'}• Training Mode → Interactive Learning → Assessment Tools
          {'\n'}• Case Search → Historical Decisions → Precedent Analysis
          {'\n'}• Add Example → Content Upload → Review and Approval
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#8e44ad',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  categoriesContainer: {
    marginBottom: 16,
  },
  categoryCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  exampleCount: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  countText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  categoryDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  caseStudiesContainer: {
    marginBottom: 16,
  },
  caseStudyItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  caseStudyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  caseStudyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    flex: 1,
    marginRight: 12,
  },
  decisionBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  decisionText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  caseStudyCategory: {
    fontSize: 14,
    color: '#7f8c8d',
    marginBottom: 8,
  },
  caseStudyMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metaText: {
    fontSize: 12,
    color: '#95a5a6',
  },
  statisticsContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  statisticItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  statisticInfo: {
    flex: 1,
  },
  violationType: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  violationCount: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  trendIndicator: {
    width: 24,
    alignItems: 'center',
  },
  trendText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  toolsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  toolButton: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  toolIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  toolTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
    textAlign: 'center',
  },
  toolDescription: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    lineHeight: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  navigationSection: {
    marginBottom: 16,
  },
  navButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 8,
  },
  navButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  featuresList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 8,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});
