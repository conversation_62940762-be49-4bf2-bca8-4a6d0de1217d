import { Link, useLocalSearchParams } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function PolicyUpdateDetail() {
  const { id } = useLocalSearchParams();

  const policyUpdateData = {
    id: id as string,
    title: 'Community Rules Update v2.3',
    type: 'Community Rules',
    status: 'Draft',
    author: 'Legal Team',
    createdDate: '2024-01-20',
    lastModified: '2024-01-22',
    effectiveDate: '2024-02-01',
    description: 'Updated harassment policies and clarified content guidelines for Stories',
    changes: [
      {
        section: 'Harassment Policy',
        type: 'Modified',
        description: 'Added specific guidelines for Stories harassment'
      },
      {
        section: 'Content Guidelines',
        type: 'Added',
        description: 'New section for Story content moderation'
      },
      {
        section: 'Enforcement Actions',
        type: 'Modified',
        description: 'Updated penalty structure for repeat violations'
      }
    ],
    approvalWorkflow: [
      { step: 'Legal Review', status: 'Completed', date: '2024-01-21' },
      { step: 'Management Approval', status: 'Pending', date: null },
      { step: 'Implementation', status: 'Pending', date: null },
      { step: 'User Notification', status: 'Pending', date: null }
    ]
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Policy Update Detail</Text>
        <Text style={styles.subtitle}>{policyUpdateData.title}</Text>
        <Link href="/admin/policies/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Policies</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides detailed management of policy updates, including change tracking,
          approval workflows, and implementation scheduling for platform policy modifications.
        </Text>

        <Text style={styles.sectionTitle}>Update Information:</Text>
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Update ID:</Text>
            <Text style={styles.infoValue}>{policyUpdateData.id}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Policy Type:</Text>
            <Text style={styles.infoValue}>{policyUpdateData.type}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Status:</Text>
            <View style={[styles.statusBadge, { backgroundColor: '#f39c12' }]}>
              <Text style={styles.statusText}>{policyUpdateData.status}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Author:</Text>
            <Text style={styles.infoValue}>{policyUpdateData.author}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Created:</Text>
            <Text style={styles.infoValue}>{policyUpdateData.createdDate}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Last Modified:</Text>
            <Text style={styles.infoValue}>{policyUpdateData.lastModified}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Effective Date:</Text>
            <Text style={styles.infoValue}>{policyUpdateData.effectiveDate}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Update Description:</Text>
        <View style={styles.descriptionCard}>
          <Text style={styles.descriptionText}>{policyUpdateData.description}</Text>
        </View>

        <Text style={styles.sectionTitle}>Change Summary:</Text>
        <View style={styles.changesContainer}>
          {policyUpdateData.changes.map((change, index) => (
            <View key={index} style={styles.changeItem}>
              <View style={styles.changeHeader}>
                <Text style={styles.changeSection}>{change.section}</Text>
                <View style={[styles.changeTypeBadge, { 
                  backgroundColor: change.type === 'Added' ? '#27ae60' : 
                                 change.type === 'Modified' ? '#f39c12' : '#e74c3c' 
                }]}>
                  <Text style={styles.changeTypeText}>{change.type}</Text>
                </View>
              </View>
              <Text style={styles.changeDescription}>{change.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Approval Workflow:</Text>
        <View style={styles.workflowContainer}>
          {policyUpdateData.approvalWorkflow.map((step, index) => (
            <View key={index} style={styles.workflowStep}>
              <View style={styles.stepIndicator}>
                <View style={[styles.stepCircle, { 
                  backgroundColor: step.status === 'Completed' ? '#27ae60' : 
                                 step.status === 'Pending' ? '#f39c12' : '#95a5a6' 
                }]}>
                  <Text style={styles.stepNumber}>{index + 1}</Text>
                </View>
                {index < policyUpdateData.approvalWorkflow.length - 1 && (
                  <View style={styles.stepConnector} />
                )}
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>{step.step}</Text>
                <Text style={[styles.stepStatus, { 
                  color: step.status === 'Completed' ? '#27ae60' : 
                        step.status === 'Pending' ? '#f39c12' : '#95a5a6' 
                }]}>
                  {step.status}
                </Text>
                {step.date && (
                  <Text style={styles.stepDate}>{step.date}</Text>
                )}
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Policy Editor:</Text>
        <View style={styles.editorCard}>
          <Text style={styles.editorTitle}>Policy Content Editor</Text>
          <View style={styles.editorPlaceholder}>
            <Text style={styles.placeholderText}>📝 Rich text editor for policy content</Text>
            <Text style={styles.placeholderSubtext}>Version control • Change tracking • Collaborative editing</Text>
          </View>
          <View style={styles.editorActions}>
            <TouchableOpacity style={styles.editorButton}>
              <Text style={styles.editorButtonText}>📄 View Current Version</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.editorButton}>
              <Text style={styles.editorButtonText}>🔍 Compare Changes</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.editorButton}>
              <Text style={styles.editorButtonText}>💾 Save Draft</Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Administrative Actions:</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionButtonText}>✓ Approve Update</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
            <Text style={styles.actionButtonText}>✗ Reject Update</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
            <Text style={styles.actionButtonText}>📝 Request Changes</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Related Navigation:</Text>
        <View style={styles.navigationSection}>
          <Link href="/admin/policies/implementation" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>🚀 Policy Implementation</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/policies/violation-examples" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>📚 Violation Examples</Text>
            </TouchableOpacity>
          </Link>
          <TouchableOpacity style={styles.navButton}>
            <Text style={styles.navButtonText}>📊 Impact Analysis</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Approve → Policy Implementation Screen
          {'\n'}• Reject → Rejection Reason Form
          {'\n'}• Request Changes → Change Request Form
          {'\n'}• Implementation → Policy Implementation Dashboard
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#9b59b6',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  descriptionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  descriptionText: {
    fontSize: 16,
    color: '#2c3e50',
    lineHeight: 24,
  },
  changesContainer: {
    marginBottom: 16,
  },
  changeItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  changeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  changeSection: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  changeTypeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  changeTypeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  changeDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  workflowContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  workflowStep: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  stepIndicator: {
    alignItems: 'center',
    marginRight: 16,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepNumber: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  stepConnector: {
    width: 2,
    height: 24,
    backgroundColor: '#ecf0f1',
    marginTop: 8,
  },
  stepContent: {
    flex: 1,
    paddingTop: 4,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  stepStatus: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  stepDate: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  editorCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  editorTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  editorPlaceholder: {
    height: 120,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  placeholderText: {
    fontSize: 16,
    color: '#7f8c8d',
    marginBottom: 4,
  },
  placeholderSubtext: {
    fontSize: 12,
    color: '#95a5a6',
  },
  editorActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  editorButton: {
    backgroundColor: '#ecf0f1',
    padding: 8,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  editorButtonText: {
    fontSize: 12,
    color: '#555',
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  navigationSection: {
    marginBottom: 16,
  },
  navButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 8,
  },
  navButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});
