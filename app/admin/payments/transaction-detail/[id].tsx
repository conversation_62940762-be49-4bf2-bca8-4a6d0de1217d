import { Link, useLocalSearchParams } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function TransactionDetail() {
  const { id } = useLocalSearchParams();

  const transactionData = {
    id: id as string,
    userId: 'user123',
    userName: '<PERSON> Do<PERSON>',
    type: 'Verification Payment',
    amount: '24,49€',
    currency: 'EUR',
    status: 'Completed',
    date: '2024-01-15 14:30:25',
    paymentMethod: 'Credit Card',
    cardLast4: '4532',
    reference: 'TXN-2024-001234',
    description: 'Public Profile Verification',
    fees: {
      platformFee: '2,45€',
      processingFee: '0,73€',
      netAmount: '21,31€'
    },
    verification: {
      status: 'Verified',
      method: 'Automatic',
      timestamp: '2024-01-15 14:31:02'
    }
  };

  const transactionHistory = [
    { timestamp: '2024-01-15 14:30:25', event: 'Payment Initiated', status: 'Processing' },
    { timestamp: '2024-01-15 14:30:45', event: 'Payment Gateway Response', status: 'Success' },
    { timestamp: '2024-01-15 14:31:02', event: 'Payment Verified', status: 'Completed' },
    { timestamp: '2024-01-15 14:31:15', event: 'Verification Process Started', status: 'Active' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Transaction Detail</Text>
        <Text style={styles.subtitle}>Transaction ID: {transactionData.id}</Text>
        <Link href="/admin/payments/transactions" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Transactions</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides comprehensive details of individual payment transactions,
          including payment verification, fee breakdown, and transaction history for
          administrative review and audit purposes.
        </Text>

        <Text style={styles.sectionTitle}>Transaction Information:</Text>
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Transaction ID:</Text>
            <Text style={styles.infoValue}>{transactionData.id}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>User:</Text>
            <Text style={styles.infoValue}>{transactionData.userName} ({transactionData.userId})</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Type:</Text>
            <Text style={styles.infoValue}>{transactionData.type}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Amount:</Text>
            <Text style={[styles.infoValue, { color: '#27ae60', fontWeight: 'bold' }]}>{transactionData.amount}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Status:</Text>
            <View style={[styles.statusBadge, { backgroundColor: '#27ae60' }]}>
              <Text style={styles.statusText}>{transactionData.status}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Date:</Text>
            <Text style={styles.infoValue}>{transactionData.date}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Reference:</Text>
            <Text style={styles.infoValue}>{transactionData.reference}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Payment Details:</Text>
        <View style={styles.paymentCard}>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Payment Method:</Text>
            <Text style={styles.paymentValue}>{transactionData.paymentMethod}</Text>
          </View>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Card Ending:</Text>
            <Text style={styles.paymentValue}>****{transactionData.cardLast4}</Text>
          </View>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Description:</Text>
            <Text style={styles.paymentValue}>{transactionData.description}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Fee Breakdown (European Format):</Text>
        <View style={styles.feeCard}>
          <View style={styles.feeRow}>
            <Text style={styles.feeLabel}>Gross Amount:</Text>
            <Text style={styles.feeValue}>{transactionData.amount}</Text>
          </View>
          <View style={styles.feeRow}>
            <Text style={styles.feeLabel}>Platform Fee:</Text>
            <Text style={styles.feeValue}>-{transactionData.fees.platformFee}</Text>
          </View>
          <View style={styles.feeRow}>
            <Text style={styles.feeLabel}>Processing Fee:</Text>
            <Text style={styles.feeValue}>-{transactionData.fees.processingFee}</Text>
          </View>
          <View style={[styles.feeRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Net Amount:</Text>
            <Text style={styles.totalValue}>{transactionData.fees.netAmount}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Verification Status:</Text>
        <View style={styles.verificationCard}>
          <View style={styles.verificationRow}>
            <Text style={styles.verificationLabel}>Status:</Text>
            <Text style={[styles.verificationValue, { color: '#27ae60' }]}>{transactionData.verification.status}</Text>
          </View>
          <View style={styles.verificationRow}>
            <Text style={styles.verificationLabel}>Method:</Text>
            <Text style={styles.verificationValue}>{transactionData.verification.method}</Text>
          </View>
          <View style={styles.verificationRow}>
            <Text style={styles.verificationLabel}>Verified At:</Text>
            <Text style={styles.verificationValue}>{transactionData.verification.timestamp}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Transaction History:</Text>
        <View style={styles.historyCard}>
          {transactionHistory.map((event, index) => (
            <View key={index} style={styles.historyItem}>
              <View style={styles.historyTimestamp}>
                <Text style={styles.timestampText}>{event.timestamp}</Text>
              </View>
              <View style={styles.historyDetails}>
                <Text style={styles.eventText}>{event.event}</Text>
                <Text style={[styles.eventStatus, { color: event.status === 'Completed' || event.status === 'Success' ? '#27ae60' : '#f39c12' }]}>
                  {event.status}
                </Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Administrative Actions:</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
            <Text style={styles.actionButtonText}>📄 Generate Receipt</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e67e22' }]}>
            <Text style={styles.actionButtonText}>🔄 Process Refund</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#9b59b6' }]}>
            <Text style={styles.actionButtonText}>📧 Contact User</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Related Navigation:</Text>
        <View style={styles.navigationSection}>
          <Link href={`/admin/users/${transactionData.userId}`} asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>👤 View User Profile</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/payments/refunds" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>💰 Refund Management</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/payments/verification" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>✅ Payment Verification</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Back → Transaction History Screen
          {'\n'}• User Profile → User Detail Screen
          {'\n'}• Refund Management → Refund Processing
          {'\n'}• Payment Verification → Verification Dashboard
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#27ae60',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  paymentCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  paymentLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  paymentValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  feeCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  feeLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  feeValue: {
    fontSize: 14,
    color: '#e74c3c',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#ecf0f1',
    paddingTop: 8,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  verificationCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  verificationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  verificationLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  verificationValue: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  historyCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  historyItem: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  historyTimestamp: {
    width: '40%',
    paddingRight: 12,
  },
  timestampText: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  historyDetails: {
    flex: 1,
  },
  eventText: {
    fontSize: 14,
    color: '#2c3e50',
    marginBottom: 4,
  },
  eventStatus: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  navigationSection: {
    marginBottom: 16,
  },
  navButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 8,
  },
  navButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});
