import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function UserInquiries() {
  const inquiryCategories = [
    {
      name: 'Account Issues',
      count: 45,
      color: '#e74c3c',
      description: 'Login, password, and account access problems'
    },
    {
      name: 'Verification Questions',
      count: 32,
      color: '#f39c12',
      description: 'Business and personal verification inquiries'
    },
    {
      name: 'Payment Support',
      count: 28,
      color: '#27ae60',
      description: 'Transaction, refund, and billing questions'
    },
    {
      name: 'Technical Issues',
      count: 19,
      color: '#3498db',
      description: 'App functionality and bug reports'
    },
    {
      name: 'Content Moderation',
      count: 15,
      color: '#9b59b6',
      description: 'Content removal and policy questions'
    },
    {
      name: 'Feature Requests',
      count: 12,
      color: '#1abc9c',
      description: 'New feature suggestions and improvements'
    }
  ];

  const recentInquiries = [
    {
      id: 'INQ-001',
      user: '<PERSON>',
      subject: 'Cannot upload verification documents',
      category: 'Verification Questions',
      priority: 'High',
      status: 'Open',
      date: '2024-01-22 14:30'
    },
    {
      id: 'INQ-002',
      user: '<PERSON>',
      subject: 'Payment not processed',
      category: 'Payment Support',
      priority: 'Medium',
      status: 'In Progress',
      date: '2024-01-22 13:15'
    },
    {
      id: 'INQ-003',
      user: 'David Chen',
      subject: 'App crashes on startup',
      category: 'Technical Issues',
      priority: 'High',
      status: 'Open',
      date: '2024-01-22 12:45'
    }
  ];

  const inquiryStats = [
    { label: 'Total Inquiries', value: '151', color: '#3498db' },
    { label: 'Open', value: '67', color: '#e74c3c' },
    { label: 'In Progress', value: '34', color: '#f39c12' },
    { label: 'Resolved Today', value: '28', color: '#27ae60' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>User Inquiries</Text>
        <Text style={styles.subtitle}>Customer Support Inquiry Management</Text>
        <Link href="/admin/support/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Support</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen manages all user inquiries and support requests, providing
          categorization, priority management, and response tracking for efficient
          customer support operations and user satisfaction.
        </Text>

        <Text style={styles.sectionTitle}>Inquiry Statistics:</Text>
        <View style={styles.statsContainer}>
          {inquiryStats.map((stat, index) => (
            <View key={index} style={[styles.statCard, { borderTopColor: stat.color }]}>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Inquiry Categories:</Text>
        <View style={styles.categoriesContainer}>
          {inquiryCategories.map((category, index) => (
            <TouchableOpacity key={index} style={[styles.categoryCard, { borderLeftColor: category.color }]}>
              <View style={styles.categoryHeader}>
                <Text style={styles.categoryName}>{category.name}</Text>
                <View style={[styles.categoryCount, { backgroundColor: category.color }]}>
                  <Text style={styles.countText}>{category.count}</Text>
                </View>
              </View>
              <Text style={styles.categoryDescription}>{category.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Recent Inquiries:</Text>
        <View style={styles.inquiriesContainer}>
          {recentInquiries.map((inquiry, index) => (
            <View key={index} style={styles.inquiryItem}>
              <View style={styles.inquiryHeader}>
                <Text style={styles.inquiryId}>{inquiry.id}</Text>
                <View style={[styles.priorityBadge, { 
                  backgroundColor: inquiry.priority === 'High' ? '#e74c3c' : 
                                 inquiry.priority === 'Medium' ? '#f39c12' : '#27ae60' 
                }]}>
                  <Text style={styles.priorityText}>{inquiry.priority}</Text>
                </View>
              </View>
              <Text style={styles.inquirySubject}>{inquiry.subject}</Text>
              <View style={styles.inquiryMeta}>
                <Text style={styles.inquiryUser}>👤 {inquiry.user}</Text>
                <Text style={styles.inquiryCategory}>📂 {inquiry.category}</Text>
              </View>
              <View style={styles.inquiryFooter}>
                <Text style={styles.inquiryDate}>{inquiry.date}</Text>
                <View style={[styles.statusBadge, { 
                  backgroundColor: inquiry.status === 'Open' ? '#e74c3c' : 
                                 inquiry.status === 'In Progress' ? '#f39c12' : '#27ae60' 
                }]}>
                  <Text style={styles.statusText}>{inquiry.status}</Text>
                </View>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Inquiry Management Tools:</Text>
        <View style={styles.toolsContainer}>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>🔍</Text>
            <Text style={styles.toolTitle}>Search & Filter</Text>
            <Text style={styles.toolDescription}>Find inquiries by user, category, or keywords</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>📊</Text>
            <Text style={styles.toolTitle}>Analytics</Text>
            <Text style={styles.toolDescription}>Inquiry trends and response metrics</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>🏷️</Text>
            <Text style={styles.toolTitle}>Bulk Actions</Text>
            <Text style={styles.toolDescription}>Mass assign, categorize, or update</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Response Templates:</Text>
        <View style={styles.templatesContainer}>
          <TouchableOpacity style={styles.templateItem}>
            <Text style={styles.templateTitle}>Account Recovery</Text>
            <Text style={styles.templateDescription}>Standard response for account access issues</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.templateItem}>
            <Text style={styles.templateTitle}>Verification Help</Text>
            <Text style={styles.templateDescription}>Guide for verification process questions</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.templateItem}>
            <Text style={styles.templateTitle}>Payment Issues</Text>
            <Text style={styles.templateDescription}>Response for transaction and billing problems</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
            <Text style={styles.actionButtonText}>📝 New Inquiry</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionButtonText}>📋 Export Data</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#f39c12' }]}>
            <Text style={styles.actionButtonText}>⚙️ Settings</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Escalation Workflow:</Text>
        <View style={styles.workflowCard}>
          <Text style={styles.workflowTitle}>Automatic Escalation Rules:</Text>
          <View style={styles.workflowRules}>
            <Text style={styles.workflowRule}>• High priority inquiries → Immediate assignment</Text>
            <Text style={styles.workflowRule}>• Unresolved after 24h → Senior support</Text>
            <Text style={styles.workflowRule}>• Payment issues → Finance team notification</Text>
            <Text style={styles.workflowRule}>• Technical bugs → Development team alert</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Related Navigation:</Text>
        <View style={styles.navigationSection}>
          <Link href="/admin/support/tickets" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>🎫 Ticket Management</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/support/user-communication" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>💬 User Communication</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/support/knowledge-base" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>📚 Knowledge Base</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Features Available:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Real-time inquiry tracking and status updates</Text>
          <Text style={styles.feature}>• Automated categorization and priority assignment</Text>
          <Text style={styles.feature}>• Response templates and canned responses</Text>
          <Text style={styles.feature}>• Escalation workflows and team notifications</Text>
          <Text style={styles.feature}>• Performance analytics and response time tracking</Text>
          <Text style={styles.feature}>• Integration with knowledge base and FAQ</Text>
          <Text style={styles.feature}>• Multi-language support for global users</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Category Selection → Filtered Inquiry List → Individual Inquiry Detail
          {'\n'}• New Inquiry → Inquiry Form → Assignment → Response
          {'\n'}• Bulk Actions → Selection → Action Confirmation → Update
          {'\n'}• Analytics → Performance Dashboard → Detailed Reports
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#17a2b8',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '22%',
    alignItems: 'center',
    borderTopWidth: 3,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
  },
  categoriesContainer: {
    marginBottom: 16,
  },
  categoryCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  categoryCount: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  countText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  categoryDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  inquiriesContainer: {
    marginBottom: 16,
  },
  inquiryItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  inquiryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  inquiryId: {
    fontSize: 12,
    color: '#7f8c8d',
    fontWeight: 'bold',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  inquirySubject: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  inquiryMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  inquiryUser: {
    fontSize: 12,
    color: '#555',
  },
  inquiryCategory: {
    fontSize: 12,
    color: '#555',
  },
  inquiryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inquiryDate: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  toolsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  toolButton: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  toolIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  toolTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
    textAlign: 'center',
  },
  toolDescription: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    lineHeight: 16,
  },
  templatesContainer: {
    marginBottom: 16,
  },
  templateItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  templateTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  templateDescription: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  workflowCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  workflowTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  workflowRules: {
    paddingLeft: 8,
  },
  workflowRule: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 4,
  },
  navigationSection: {
    marginBottom: 16,
  },
  navButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 8,
  },
  navButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  featuresList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 8,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});
