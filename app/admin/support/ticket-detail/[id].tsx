import { Link, useLocalSearchParams } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function TicketDetail() {
  const { id } = useLocalSearchParams();

  const ticketData = {
    id: id as string,
    title: 'Unable to verify business account',
    category: 'Account Verification',
    priority: 'High',
    status: 'In Progress',
    userId: 'user789',
    userName: '<PERSON>',
    userEmail: '<EMAIL>',
    createdDate: '2024-01-20 09:15:30',
    lastUpdated: '2024-01-22 14:30:45',
    assignedTo: 'Support Agent John',
    description: 'User is unable to complete business account verification. Documents are being rejected despite meeting all requirements.',
    tags: ['verification', 'business', 'documents', 'urgent']
  };

  const ticketHistory = [
    {
      timestamp: '2024-01-20 09:15:30',
      action: 'Ticket Created',
      user: '<PERSON>',
      details: 'Initial ticket submission'
    },
    {
      timestamp: '2024-01-20 10:30:15',
      action: 'Assigned',
      user: 'System',
      details: 'Assigned to Support Agent John'
    },
    {
      timestamp: '2024-01-21 14:20:00',
      action: 'Status Updated',
      user: 'Support Agent John',
      details: 'Changed status from New to In Progress'
    },
    {
      timestamp: '2024-01-22 14:30:45',
      action: 'Comment Added',
      user: 'Support Agent John',
      details: 'Requested additional documentation from user'
    }
  ];

  const relatedTickets = [
    { id: 'TKT-001234', title: 'Business verification issues', status: 'Resolved' },
    { id: 'TKT-001189', title: 'Document upload problems', status: 'Closed' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Ticket Detail</Text>
        <Text style={styles.subtitle}>Ticket #{ticketData.id}</Text>
        <Link href="/admin/support/tickets" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Tickets</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides comprehensive ticket management with detailed information,
          communication history, resolution tools, and user interaction capabilities
          for efficient customer support operations.
        </Text>

        <Text style={styles.sectionTitle}>Ticket Information:</Text>
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Ticket ID:</Text>
            <Text style={styles.infoValue}>{ticketData.id}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Title:</Text>
            <Text style={styles.infoValue}>{ticketData.title}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Category:</Text>
            <Text style={styles.infoValue}>{ticketData.category}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Priority:</Text>
            <View style={[styles.priorityBadge, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.priorityText}>{ticketData.priority}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Status:</Text>
            <View style={[styles.statusBadge, { backgroundColor: '#f39c12' }]}>
              <Text style={styles.statusText}>{ticketData.status}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Created:</Text>
            <Text style={styles.infoValue}>{ticketData.createdDate}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Last Updated:</Text>
            <Text style={styles.infoValue}>{ticketData.lastUpdated}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Assigned To:</Text>
            <Text style={styles.infoValue}>{ticketData.assignedTo}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>User Information:</Text>
        <View style={styles.userCard}>
          <View style={styles.userRow}>
            <Text style={styles.userLabel}>User ID:</Text>
            <Text style={styles.userValue}>{ticketData.userId}</Text>
          </View>
          <View style={styles.userRow}>
            <Text style={styles.userLabel}>Name:</Text>
            <Text style={styles.userValue}>{ticketData.userName}</Text>
          </View>
          <View style={styles.userRow}>
            <Text style={styles.userLabel}>Email:</Text>
            <Text style={styles.userValue}>{ticketData.userEmail}</Text>
          </View>
          <Link href={`/admin/users/${ticketData.userId}`} asChild>
            <TouchableOpacity style={styles.userProfileButton}>
              <Text style={styles.userProfileText}>👤 View User Profile</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Ticket Description:</Text>
        <View style={styles.descriptionCard}>
          <Text style={styles.descriptionText}>{ticketData.description}</Text>
        </View>

        <Text style={styles.sectionTitle}>Tags:</Text>
        <View style={styles.tagsContainer}>
          {ticketData.tags.map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Ticket History:</Text>
        <View style={styles.historyContainer}>
          {ticketHistory.map((event, index) => (
            <View key={index} style={styles.historyItem}>
              <View style={styles.historyTimestamp}>
                <Text style={styles.timestampText}>{event.timestamp}</Text>
              </View>
              <View style={styles.historyContent}>
                <Text style={styles.historyAction}>{event.action}</Text>
                <Text style={styles.historyUser}>by {event.user}</Text>
                <Text style={styles.historyDetails}>{event.details}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Communication Tools:</Text>
        <View style={styles.communicationCard}>
          <Text style={styles.communicationTitle}>Response Options:</Text>
          <View style={styles.responseButtons}>
            <TouchableOpacity style={[styles.responseButton, { backgroundColor: '#3498db' }]}>
              <Text style={styles.responseButtonText}>📧 Send Email</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.responseButton, { backgroundColor: '#27ae60' }]}>
              <Text style={styles.responseButtonText}>💬 Internal Note</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.responseButton, { backgroundColor: '#f39c12' }]}>
              <Text style={styles.responseButtonText}>📞 Schedule Call</Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Ticket Actions:</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionButtonText}>✓ Resolve</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
            <Text style={styles.actionButtonText}>✗ Close</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#9b59b6' }]}>
            <Text style={styles.actionButtonText}>↗ Escalate</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#34495e' }]}>
            <Text style={styles.actionButtonText}>👥 Reassign</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Related Tickets:</Text>
        <View style={styles.relatedContainer}>
          {relatedTickets.map((ticket, index) => (
            <View key={index} style={styles.relatedItem}>
              <Text style={styles.relatedId}>{ticket.id}</Text>
              <Text style={styles.relatedTitle}>{ticket.title}</Text>
              <View style={[styles.relatedStatus, { 
                backgroundColor: ticket.status === 'Resolved' ? '#27ae60' : '#95a5a6' 
              }]}>
                <Text style={styles.relatedStatusText}>{ticket.status}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Quick Navigation:</Text>
        <View style={styles.navigationSection}>
          <Link href="/admin/support/knowledge-base" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>📚 Knowledge Base</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/support/user-communication" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>💬 User Communication</Text>
            </TouchableOpacity>
          </Link>
          <TouchableOpacity style={styles.navButton}>
            <Text style={styles.navButtonText}>📊 Ticket Analytics</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Resolve → Resolution Confirmation → User Notification
          {'\n'}• Escalate → Escalation Form → Senior Support Assignment
          {'\n'}• Reassign → Agent Selection → Transfer Notification
          {'\n'}• User Profile → User Management → Account Details
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#16a085',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  userCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  userRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  userLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  userValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  userProfileButton: {
    backgroundColor: '#3498db',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 8,
  },
  userProfileText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  descriptionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  descriptionText: {
    fontSize: 16,
    color: '#2c3e50',
    lineHeight: 24,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  tag: {
    backgroundColor: '#ecf0f1',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#555',
  },
  historyContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  historyItem: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  historyTimestamp: {
    width: '35%',
    paddingRight: 12,
  },
  timestampText: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  historyContent: {
    flex: 1,
  },
  historyAction: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 2,
  },
  historyUser: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 4,
  },
  historyDetails: {
    fontSize: 14,
    color: '#555',
  },
  communicationCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  communicationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  responseButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  responseButton: {
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  responseButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 8,
    width: '22%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  relatedContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  relatedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  relatedId: {
    fontSize: 12,
    color: '#7f8c8d',
    width: 80,
  },
  relatedTitle: {
    fontSize: 14,
    color: '#2c3e50',
    flex: 1,
    marginHorizontal: 12,
  },
  relatedStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  relatedStatusText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  navigationSection: {
    marginBottom: 16,
  },
  navButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 8,
  },
  navButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});
