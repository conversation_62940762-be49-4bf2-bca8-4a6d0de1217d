import { Link, useLocalSearchParams } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function ArticleDetail() {
  const { id } = useLocalSearchParams();

  const articleData = {
    id: id as string,
    title: 'How to Verify Your Business Account',
    category: 'Account Verification',
    status: 'Published',
    author: 'Support Team',
    createdDate: '2024-01-10',
    lastUpdated: '2024-01-20',
    views: 1247,
    helpfulVotes: 89,
    notHelpfulVotes: 12,
    tags: ['verification', 'business', 'documents', 'guide'],
    content: `This comprehensive guide will walk you through the business account verification process step by step.

## Requirements

Before starting the verification process, ensure you have:
- Valid business registration documents
- Government-issued ID of the business owner
- Proof of business address
- Business tax identification number

## Step-by-Step Process

1. **Navigate to Account Settings**
   Go to your profile and select "Account Verification"

2. **Choose Business Verification**
   Select "Business Account" from the verification options

3. **Upload Documents**
   Upload clear photos of all required documents

4. **Payment**
   Complete the verification fee payment (varies by region)

5. **Review Process**
   Wait for our team to review your submission (typically 2-3 business days)

## Common Issues

- **Document Quality**: Ensure all documents are clear and readable
- **Name Matching**: Business name must match across all documents
- **Address Verification**: Business address must be verifiable

## Need Help?

If you encounter issues during verification, please contact our support team.`
  };

  const relatedArticles = [
    { id: 'art-002', title: 'Personal Account Verification Guide', views: 892 },
    { id: 'art-003', title: 'Document Upload Requirements', views: 654 },
    { id: 'art-004', title: 'Verification Troubleshooting', views: 423 }
  ];

  const articleMetrics = [
    { label: 'Total Views', value: articleData.views, color: '#3498db' },
    { label: 'Helpful Votes', value: articleData.helpfulVotes, color: '#27ae60' },
    { label: 'Not Helpful', value: articleData.notHelpfulVotes, color: '#e74c3c' },
    { label: 'Success Rate', value: '88%', color: '#f39c12' }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Knowledge Base Article</Text>
        <Text style={styles.subtitle}>{articleData.title}</Text>
        <Link href="/admin/support/knowledge-base" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Knowledge Base</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides detailed management of knowledge base articles,
          including content editing, performance analytics, user feedback analysis,
          and article optimization tools for improved customer self-service.
        </Text>

        <Text style={styles.sectionTitle}>Article Information:</Text>
        <View style={styles.infoCard}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Article ID:</Text>
            <Text style={styles.infoValue}>{articleData.id}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Category:</Text>
            <Text style={styles.infoValue}>{articleData.category}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Status:</Text>
            <View style={[styles.statusBadge, { backgroundColor: '#27ae60' }]}>
              <Text style={styles.statusText}>{articleData.status}</Text>
            </View>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Author:</Text>
            <Text style={styles.infoValue}>{articleData.author}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Created:</Text>
            <Text style={styles.infoValue}>{articleData.createdDate}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Last Updated:</Text>
            <Text style={styles.infoValue}>{articleData.lastUpdated}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Article Performance:</Text>
        <View style={styles.metricsContainer}>
          {articleMetrics.map((metric, index) => (
            <View key={index} style={[styles.metricCard, { borderTopColor: metric.color }]}>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricLabel}>{metric.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Article Tags:</Text>
        <View style={styles.tagsContainer}>
          {articleData.tags.map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>{tag}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Article Content:</Text>
        <View style={styles.contentCard}>
          <Text style={styles.contentTitle}>Content Preview:</Text>
          <View style={styles.contentPreview}>
            <Text style={styles.contentText}>{articleData.content}</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Content Management Tools:</Text>
        <View style={styles.toolsContainer}>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>✏️</Text>
            <Text style={styles.toolTitle}>Edit Article</Text>
            <Text style={styles.toolDescription}>Rich text editor with formatting</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>🔍</Text>
            <Text style={styles.toolTitle}>SEO Analysis</Text>
            <Text style={styles.toolDescription}>Optimize for search and discovery</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>📊</Text>
            <Text style={styles.toolTitle}>Analytics</Text>
            <Text style={styles.toolDescription}>Detailed performance metrics</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>User Feedback Analysis:</Text>
        <View style={styles.feedbackCard}>
          <View style={styles.feedbackHeader}>
            <Text style={styles.feedbackTitle}>Feedback Summary</Text>
            <Text style={styles.feedbackScore}>
              {Math.round((articleData.helpfulVotes / (articleData.helpfulVotes + articleData.notHelpfulVotes)) * 100)}% Helpful
            </Text>
          </View>
          <View style={styles.feedbackStats}>
            <View style={styles.feedbackStat}>
              <Text style={styles.feedbackStatValue}>{articleData.helpfulVotes}</Text>
              <Text style={styles.feedbackStatLabel}>👍 Helpful</Text>
            </View>
            <View style={styles.feedbackStat}>
              <Text style={styles.feedbackStatValue}>{articleData.notHelpfulVotes}</Text>
              <Text style={styles.feedbackStatLabel}>👎 Not Helpful</Text>
            </View>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Related Articles:</Text>
        <View style={styles.relatedContainer}>
          {relatedArticles.map((article, index) => (
            <View key={index} style={styles.relatedItem}>
              <Text style={styles.relatedId}>{article.id}</Text>
              <Text style={styles.relatedTitle}>{article.title}</Text>
              <Text style={styles.relatedViews}>{article.views} views</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Article Actions:</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
            <Text style={styles.actionButtonText}>✏️ Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionButtonText}>📤 Publish</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#f39c12' }]}>
            <Text style={styles.actionButtonText}>📋 Duplicate</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#e74c3c' }]}>
            <Text style={styles.actionButtonText}>🗑️ Archive</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Content Optimization:</Text>
        <View style={styles.optimizationCard}>
          <Text style={styles.optimizationTitle}>Improvement Suggestions:</Text>
          <View style={styles.suggestionsList}>
            <Text style={styles.suggestion}>• Add more visual elements (screenshots, diagrams)</Text>
            <Text style={styles.suggestion}>• Include video tutorial link</Text>
            <Text style={styles.suggestion}>• Update with latest UI changes</Text>
            <Text style={styles.suggestion}>• Add FAQ section for common questions</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Quick Navigation:</Text>
        <View style={styles.navigationSection}>
          <Link href="/admin/support/knowledge-base" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>📚 All Articles</Text>
            </TouchableOpacity>
          </Link>
          <TouchableOpacity style={styles.navButton}>
            <Text style={styles.navButtonText}>📊 Article Analytics</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.navButton}>
            <Text style={styles.navButtonText}>💬 User Comments</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Edit → Article Editor → Preview → Publish
          {'\n'}• Analytics → Performance Dashboard → Optimization Recommendations
          {'\n'}• Duplicate → Article Template → New Article Creation
          {'\n'}• Archive → Confirmation → Article Archive
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#1abc9c',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
    textAlign: 'center',
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '22%',
    alignItems: 'center',
    borderTopWidth: 3,
  },
  metricValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  tag: {
    backgroundColor: '#ecf0f1',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#555',
  },
  contentCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  contentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  contentPreview: {
    maxHeight: 200,
    overflow: 'hidden',
  },
  contentText: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  toolsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  toolButton: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  toolIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  toolTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
    textAlign: 'center',
  },
  toolDescription: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    lineHeight: 16,
  },
  feedbackCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feedbackHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  feedbackTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  feedbackScore: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  feedbackStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  feedbackStat: {
    alignItems: 'center',
  },
  feedbackStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  feedbackStatLabel: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  relatedContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  relatedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  relatedId: {
    fontSize: 12,
    color: '#7f8c8d',
    width: 60,
  },
  relatedTitle: {
    fontSize: 14,
    color: '#2c3e50',
    flex: 1,
    marginHorizontal: 12,
  },
  relatedViews: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 8,
    width: '22%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  optimizationCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  optimizationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  suggestionsList: {
    paddingLeft: 8,
  },
  suggestion: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 4,
  },
  navigationSection: {
    marginBottom: 16,
  },
  navButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 8,
  },
  navButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});
