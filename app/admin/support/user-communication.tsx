import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function UserCommunication() {
  const communicationChannels = [
    {
      name: 'Email Support',
      description: 'Direct email communication with users',
      status: 'Active',
      responseTime: '2.3 hours',
      volume: 145,
      color: '#3498db'
    },
    {
      name: 'In-App Messaging',
      description: 'Real-time messaging within the application',
      status: 'Active',
      responseTime: '15 minutes',
      volume: 89,
      color: '#27ae60'
    },
    {
      name: 'Push Notifications',
      description: 'System-wide announcements and updates',
      status: 'Active',
      responseTime: 'Instant',
      volume: 1247,
      color: '#f39c12'
    },
    {
      name: 'SMS Alerts',
      description: 'Critical notifications via text message',
      status: 'Active',
      responseTime: 'Instant',
      volume: 67,
      color: '#e74c3c'
    }
  ];

  const recentCommunications = [
    {
      id: 'COM-001',
      user: '<PERSON>',
      channel: 'Email',
      subject: 'Verification Status Update',
      status: 'Sent',
      timestamp: '2024-01-22 15:30',
      type: 'Response'
    },
    {
      id: 'COM-002',
      user: '<PERSON>',
      channel: 'In-App',
      subject: 'Payment Confirmation',
      status: 'Delivered',
      timestamp: '2024-01-22 14:45',
      type: 'Notification'
    },
    {
      id: 'COM-003',
      user: 'Carol Davis',
      channel: 'Push',
      subject: 'Policy Update Notification',
      status: 'Delivered',
      timestamp: '2024-01-22 13:20',
      type: 'Announcement'
    }
  ];

  const communicationStats = [
    { label: 'Messages Sent Today', value: '342', color: '#3498db' },
    { label: 'Avg Response Time', value: '1.8h', color: '#27ae60' },
    { label: 'Delivery Rate', value: '98.5%', color: '#f39c12' },
    { label: 'User Satisfaction', value: '4.7/5', color: '#e74c3c' }
  ];

  const messageTemplates = [
    {
      name: 'Welcome Message',
      category: 'Onboarding',
      usage: 'High',
      lastUpdated: '2024-01-15'
    },
    {
      name: 'Verification Approved',
      category: 'Account Status',
      usage: 'Medium',
      lastUpdated: '2024-01-18'
    },
    {
      name: 'Payment Received',
      category: 'Transactions',
      usage: 'High',
      lastUpdated: '2024-01-20'
    },
    {
      name: 'Policy Violation Warning',
      category: 'Moderation',
      usage: 'Low',
      lastUpdated: '2024-01-12'
    }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>User Communication</Text>
        <Text style={styles.subtitle}>Multi-Channel Communication Management</Text>
        <Link href="/admin/support/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Support</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen manages all user communication channels including email, in-app messaging,
          push notifications, and SMS alerts. It provides template management, delivery tracking,
          and communication analytics for effective user engagement.
        </Text>

        <Text style={styles.sectionTitle}>Communication Statistics:</Text>
        <View style={styles.statsContainer}>
          {communicationStats.map((stat, index) => (
            <View key={index} style={[styles.statCard, { borderTopColor: stat.color }]}>
              <Text style={styles.statValue}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Communication Channels:</Text>
        <View style={styles.channelsContainer}>
          {communicationChannels.map((channel, index) => (
            <View key={index} style={[styles.channelCard, { borderLeftColor: channel.color }]}>
              <View style={styles.channelHeader}>
                <Text style={styles.channelName}>{channel.name}</Text>
                <View style={[styles.statusBadge, { backgroundColor: '#27ae60' }]}>
                  <Text style={styles.statusText}>{channel.status}</Text>
                </View>
              </View>
              <Text style={styles.channelDescription}>{channel.description}</Text>
              <View style={styles.channelMetrics}>
                <View style={styles.metric}>
                  <Text style={styles.metricLabel}>Response Time:</Text>
                  <Text style={styles.metricValue}>{channel.responseTime}</Text>
                </View>
                <View style={styles.metric}>
                  <Text style={styles.metricLabel}>Volume (24h):</Text>
                  <Text style={styles.metricValue}>{channel.volume}</Text>
                </View>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Recent Communications:</Text>
        <View style={styles.communicationsContainer}>
          {recentCommunications.map((comm, index) => (
            <View key={index} style={styles.communicationItem}>
              <View style={styles.communicationHeader}>
                <Text style={styles.communicationId}>{comm.id}</Text>
                <View style={[styles.typeBadge, { 
                  backgroundColor: comm.type === 'Response' ? '#3498db' : 
                                 comm.type === 'Notification' ? '#27ae60' : '#f39c12' 
                }]}>
                  <Text style={styles.typeText}>{comm.type}</Text>
                </View>
              </View>
              <Text style={styles.communicationSubject}>{comm.subject}</Text>
              <View style={styles.communicationMeta}>
                <Text style={styles.communicationUser}>👤 {comm.user}</Text>
                <Text style={styles.communicationChannel}>📱 {comm.channel}</Text>
              </View>
              <View style={styles.communicationFooter}>
                <Text style={styles.communicationTime}>{comm.timestamp}</Text>
                <View style={[styles.deliveryStatus, { 
                  backgroundColor: comm.status === 'Sent' ? '#f39c12' : '#27ae60' 
                }]}>
                  <Text style={styles.deliveryText}>{comm.status}</Text>
                </View>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Message Templates:</Text>
        <View style={styles.templatesContainer}>
          {messageTemplates.map((template, index) => (
            <View key={index} style={styles.templateItem}>
              <View style={styles.templateHeader}>
                <Text style={styles.templateName}>{template.name}</Text>
                <View style={[styles.usageBadge, { 
                  backgroundColor: template.usage === 'High' ? '#e74c3c' : 
                                 template.usage === 'Medium' ? '#f39c12' : '#27ae60' 
                }]}>
                  <Text style={styles.usageText}>{template.usage}</Text>
                </View>
              </View>
              <Text style={styles.templateCategory}>Category: {template.category}</Text>
              <Text style={styles.templateUpdated}>Last updated: {template.lastUpdated}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Communication Tools:</Text>
        <View style={styles.toolsContainer}>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>📝</Text>
            <Text style={styles.toolTitle}>Compose Message</Text>
            <Text style={styles.toolDescription}>Create new user communication</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>📋</Text>
            <Text style={styles.toolTitle}>Template Editor</Text>
            <Text style={styles.toolDescription}>Manage message templates</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.toolButton}>
            <Text style={styles.toolIcon}>📊</Text>
            <Text style={styles.toolTitle}>Analytics</Text>
            <Text style={styles.toolDescription}>Communication performance metrics</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Bulk Communication:</Text>
        <View style={styles.bulkCard}>
          <Text style={styles.bulkTitle}>Mass Communication Options:</Text>
          <View style={styles.bulkOptions}>
            <TouchableOpacity style={styles.bulkOption}>
              <Text style={styles.bulkOptionText}>📢 System Announcement</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.bulkOption}>
              <Text style={styles.bulkOptionText}>🔔 Policy Update</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.bulkOption}>
              <Text style={styles.bulkOptionText}>⚠️ Security Alert</Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Automation Rules:</Text>
        <View style={styles.automationCard}>
          <Text style={styles.automationTitle}>Active Automation Rules:</Text>
          <View style={styles.automationRules}>
            <Text style={styles.automationRule}>• Welcome email → New user registration</Text>
            <Text style={styles.automationRule}>• Verification reminder → 24h after document upload</Text>
            <Text style={styles.automationRule}>• Payment confirmation → Successful transaction</Text>
            <Text style={styles.automationRule}>• Inactivity notification → 30 days no login</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#3498db' }]}>
            <Text style={styles.actionButtonText}>📧 Send Email</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionButtonText}>💬 In-App Message</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.actionButton, { backgroundColor: '#f39c12' }]}>
            <Text style={styles.actionButtonText}>🔔 Push Notification</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Related Navigation:</Text>
        <View style={styles.navigationSection}>
          <Link href="/admin/support/tickets" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>🎫 Support Tickets</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/support/user-inquiries" asChild>
            <TouchableOpacity style={styles.navButton}>
              <Text style={styles.navButtonText}>❓ User Inquiries</Text>
            </TouchableOpacity>
          </Link>
          <TouchableOpacity style={styles.navButton}>
            <Text style={styles.navButtonText}>📈 Communication Reports</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Features Available:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Multi-channel communication management</Text>
          <Text style={styles.feature}>• Template-based messaging with personalization</Text>
          <Text style={styles.feature}>• Automated communication workflows</Text>
          <Text style={styles.feature}>• Delivery tracking and read receipts</Text>
          <Text style={styles.feature}>• Bulk messaging and announcements</Text>
          <Text style={styles.feature}>• Communication analytics and reporting</Text>
          <Text style={styles.feature}>• Multi-language support and localization</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Compose → Channel Selection → Template/Custom → Send → Tracking
          {'\n'}• Template Management → Edit → Preview → Save → Usage Analytics
          {'\n'}• Bulk Communication → Audience Selection → Message → Schedule → Send
          {'\n'}• Analytics → Performance Metrics → Detailed Reports → Optimization
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#6c5ce7',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '22%',
    alignItems: 'center',
    borderTopWidth: 3,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 11,
    color: '#7f8c8d',
    textAlign: 'center',
  },
  channelsContainer: {
    marginBottom: 16,
  },
  channelCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
  },
  channelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  channelName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  channelDescription: {
    fontSize: 14,
    color: '#555',
    marginBottom: 12,
  },
  channelMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metric: {
    flex: 1,
  },
  metricLabel: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  metricValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  communicationsContainer: {
    marginBottom: 16,
  },
  communicationItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  communicationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  communicationId: {
    fontSize: 12,
    color: '#7f8c8d',
    fontWeight: 'bold',
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  typeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  communicationSubject: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  communicationMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  communicationUser: {
    fontSize: 12,
    color: '#555',
  },
  communicationChannel: {
    fontSize: 12,
    color: '#555',
  },
  communicationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  communicationTime: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  deliveryStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  deliveryText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  templatesContainer: {
    marginBottom: 16,
  },
  templateItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  templateName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  usageBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  usageText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  templateCategory: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 4,
  },
  templateUpdated: {
    fontSize: 12,
    color: '#95a5a6',
  },
  toolsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  toolButton: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  toolIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  toolTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
    textAlign: 'center',
  },
  toolDescription: {
    fontSize: 12,
    color: '#7f8c8d',
    textAlign: 'center',
    lineHeight: 16,
  },
  bulkCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  bulkTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  bulkOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  bulkOption: {
    backgroundColor: '#ecf0f1',
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  bulkOptionText: {
    fontSize: 12,
    color: '#555',
    textAlign: 'center',
  },
  automationCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  automationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  automationRules: {
    paddingLeft: 8,
  },
  automationRule: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionButton: {
    padding: 12,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  navigationSection: {
    marginBottom: 16,
  },
  navButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 8,
  },
  navButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  featuresList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    marginBottom: 8,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});
